<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="4b271906-607b-4593-9780-2e6d137fad0e" name="Changes" comment="Sound Visualiser v1.0.2&#10;&#10;Add CI/CD pipelines">
      <change afterPath="$PROJECT_DIR$/.eslintrc.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.github/ISSUE_TEMPLATE/bug_report.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.github/ISSUE_TEMPLATE/feature_request.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.github/dependabot.yml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.github/pull_request_template.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.github/workflows/ci-cd.yml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.github/workflows/pages.yml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.github/workflows/security.yml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.gitignore" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.prettierrc" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/lighthouserc.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/netlify.toml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/package.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/windsurf_deployment.yaml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/README.md" beforeDir="false" afterPath="$PROJECT_DIR$/README.md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/install.sh" beforeDir="false" afterPath="$PROJECT_DIR$/install.sh" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GithubPullRequestsUISettings"><![CDATA[{
  "selectedUrlAndAccountId": {
    "url": "https://github.com/shirishpothi/soundvisualiser",
    "accountId": "de4f691a-ab48-4632-af9a-abf457ba15e5"
  }
}]]></component>
  <component name="MaliciousPackageCheckinHandlerState">
    <option name="packageCheck" value="true" />
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 0
}]]></component>
  <component name="ProjectId" id="2xrPSybuRgSxXOc3WxkVqjwOPaX" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "git-widget-placeholder": "Rebasing main",
    "junie.onboarding.icon.badge.shown": "true",
    "last_opened_file_path": "/Users/<USER>/PycharmProjects/Random Projects/Sound Visualiser",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "org.jetbrains.plugins.github.ui.GithubSettingsConfigurable",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-6a121458b545-JavaScript-PY-251.25410.159" />
        <option value="bundled-python-sdk-e0ed3721d81e-36ea0e71a18c-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-251.25410.159" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="4b271906-607b-4593-9780-2e6d137fad0e" name="Changes" comment="" />
      <created>1748693222173</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1748693222173</updated>
      <workItem from="1748693223388" duration="302000" />
    </task>
    <task id="LOCAL-00001" summary="Sound Visualiser v1.0.2&#10;&#10;Add CI/CD pipelines">
      <option name="closed" value="true" />
      <created>1748693419788</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1748693419788</updated>
    </task>
    <option name="localTasksCounter" value="2" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="Sound Visualiser v1.0.2&#10;&#10;Add CI/CD pipelines" />
    <option name="LAST_COMMIT_MESSAGE" value="Sound Visualiser v1.0.2&#10;&#10;Add CI/CD pipelines" />
  </component>
</project>